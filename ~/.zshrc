# ============================================================================
# Oh My Zsh Configuration
# ============================================================================
export ZSH="$HOME/.oh-my-zsh"
zstyle ':omz:update' mode reminder
plugins=(git)
source $ZSH/oh-my-zsh.sh

# ============================================================================
# Pure Prompt Configuration
# ============================================================================
fpath+=("/opt/homebrew/share/zsh/site-functions")
autoload -U promptinit
promptinit

# Pure prompt styling
zstyle :prompt:pure:git:branch color 118
prompt pure

# ============================================================================
# Environment Variables
# ============================================================================
export GPG_TTY=$(tty)
export NODE_TLS_REJECT_UNAUTHORIZED=0

# ============================================================================
# PATH Configuration
# ============================================================================
export PATH=$PATH:/opt/homebrew/bin
export PATH="/usr/local/opt/openjdk@11/bin:$PATH"
export PATH=$HOME/.nodebrew/current/bin:$PATH
export PATH="$HOME/bin:$PATH"
export PATH="/usr/local/opt/libxslt/bin:$PATH"
export PATH="/usr/local/opt/ruby/bin:$PATH"

# ============================================================================
# Development Environment Variables
# ============================================================================
export CPPFLAGS="-I/usr/local/opt/openjdk@11/include"
export LDFLAGS="-L/usr/local/opt/libxslt/lib"
export CPPFLAGS="-I/usr/local/opt/libxslt/include"
export PKG_CONFIG_PATH="/usr/local/opt/libxslt/lib/pkgconfig"

# ============================================================================
# Git Aliases
# ============================================================================
alias rebase="HUSKY=0 git pull --rebase origin test"
alias rebasemain="HUSKY=0 git pull --rebase origin main"
alias pushnew="HUSKY=0 git push origin head -u"
alias pushold="HUSKY=0 git push"
alias pushnewrebase="HUSKY=0 git push origin head -u --force-with-lease"
alias pusholdrebase="HUSKY=0 git push --force-with-lease"
alias stashin="git stash"
alias stashout="git stash pop"
alias stash='git stash'
alias pop='git stash pop'
alias commit='git commit -S -m'
alias resetauthor='git commit --amend --reset-author --no-edit && git rebase --continue'
alias undo='git reset --soft HEAD^'
alias pull='git stash && git pull & git stash pop'
alias gitlog='git log --oneline'

# ============================================================================
# Development Aliases
# ============================================================================
alias deploy="npm run sls -- deploy -s bradley --verbose"
alias migrate='serverless invoke --function DbMigration --stage bradley --data "{\"migrateType\": \"latest\"}" --log'
alias webbuild='set -a && source .env && set +a && node ./build-server/app.js'
alias changebase='git rebase --onto next-release test <FEATURE_BRANCH>'

# ============================================================================
# AWS & Infrastructure Aliases
# ============================================================================
alias syncs3="aws s3 rm s3://axa-li-jp-ccifa-commission-bradley/output --recursive && aws s3 sync s3://axa-li-jp-ccifa-commission-dev/output s3://axa-li-jp-ccifa-commission-bradley/output"
alias pastetoken="pbpaste > ~/.aws/credentials"

# ============================================================================
# Server Access Aliases
# ============================================================================
alias ift='ssh ftpsq@10.239.8.92 -i ~/.ssh/ift_server.key'
alias iftdev='ssh bradley.wong@10.239.8.134 -i ~/.ssh/ift_server_dev.key'
alias downloadift='scp -p -i ~/.ssh/ift_server.key ftpsq@10.239.8.92:../IFT058/MONTHLY/20230913営業社員退職金計算書.csv ~/Desktop'
alias listift='ls -ltr'

# ============================================================================
# Application Aliases
# ============================================================================
alias dataloader="~/dataloader/v56.0.6/dataloader.app/Contents/MacOS/dataloader"

# ============================================================================
# Custom Functions
# ============================================================================
vpn() {
  /Users/<USER>/Github/cisco-autoconnect/cisco-autoconnect.sh $1
}

logon() {
  /Users/<USER>/Github/mac-pclog/sendEvents.sh logon $1 $2 $3
}

logoff() {
  /Users/<USER>/Github/mac-pclog/sendEvents.sh logoff $1 $2 $3
}

# ============================================================================
# SDKMAN Configuration
# ============================================================================
export SDKMAN_DIR="$HOME/.sdkman"
[[ -s "$HOME/.sdkman/bin/sdkman-init.sh" ]] && source "$HOME/.sdkman/bin/sdkman-init.sh"

# ============================================================================
# NVM Configuration
# ============================================================================
export NVM_DIR="$HOME/.nvm"
[ -s "/opt/homebrew/opt/nvm/nvm.sh" ] && \. "/opt/homebrew/opt/nvm/nvm.sh"
[ -s "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm" ] && \. "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm"

# Auto-switch Node versions based on .nvmrc
autoload -U add-zsh-hook
load-nvmrc() {
  local nvmrc_path
  nvmrc_path="$(nvm_find_nvmrc)"

  if [ -n "$nvmrc_path" ]; then
    local nvmrc_node_version
    nvmrc_node_version=$(nvm version "$(cat "${nvmrc_path}")")

    if [ "$nvmrc_node_version" = "N/A" ]; then
      nvm install
    elif [ "$nvmrc_node_version" != "$(nvm version)" ]; then
      nvm use
    fi
  elif [ -n "$(PWD=$OLDPWD nvm_find_nvmrc)" ] && [ "$(nvm version)" != "$(nvm version default)" ]; then
    echo "Reverting to nvm default version"
    nvm use default
  fi
}
load-nvmrc
add-zsh-hook chpwd load-nvmrc

# ============================================================================
# Bun Configuration
# ============================================================================
export BUN_INSTALL="$HOME/.bun"
export PATH="$BUN_INSTALL/bin:$PATH"
[ -s "/Users/<USER>/.bun/_bun" ] && source "/Users/<USER>/.bun/_bun"

# ============================================================================
# Additional Tools
# ============================================================================
# Syntax highlighting (if installed)
[ -f /opt/homebrew/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh ] && source /opt/homebrew/share/zsh-syntax-highlighting/zsh-syntax-highlighting.zsh

# AWS CLI completion
complete -C aws_completer aws

# OpenShift CLI completion (if installed)
if [ ]; then
  source <(oc completion zsh)
  compdef _oc oc
fi
